.dashboard-container {
  min-height: calc(100vh - 70px);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  padding: 2rem;
}
.coming-soon-wrapper {
  width: 100%;
  max-width: 800px;
}

.coming-soon-card {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  padding: 3rem;
  text-align: center;
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-5px);
  }
}

.coming-soon-icon {
  font-size: 4rem;
  color: #0d6efd;
  margin-bottom: 1.5rem;

  i {
    background: linear-gradient(45deg, #0d6efd, #6610f2);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

.coming-soon-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #212529;
  margin-bottom: 1rem;
}

.coming-soon-description {
  font-size: 1.1rem;
  color: #6c757d;
  margin-bottom: 2rem;
  line-height: 1.6;
}

@media (max-width: 768px) {
  .coming-soon-card {
    padding: 2rem;
  }

  .coming-soon-title {
    font-size: 2rem;
  }
}
