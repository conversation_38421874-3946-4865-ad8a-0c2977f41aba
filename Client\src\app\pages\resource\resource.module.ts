import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { ResourceRoutingModule } from './resource-routing.module';
import { ResourceListComponent } from './resource-list/resource-list.component';
import { AddEditListComponent } from './add-edit-list/add-edit-list.component';
import { ResourceDetailsComponent } from './resource-details/resource-details.component';

// PrimeNG Modules
import { ToastModule } from 'primeng/toast';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { ButtonModule } from 'primeng/button';
import { RadioButtonModule } from 'primeng/radiobutton';
import { InputTextModule } from 'primeng/inputtext';
import { MessageModule } from 'primeng/message';
import { CardModule } from 'primeng/card';
import { DividerModule } from 'primeng/divider';
import { BadgeModule } from 'primeng/badge';
import { FileUploadModule } from 'primeng/fileupload';
import { DropdownModule } from 'primeng/dropdown';
import { CheckboxModule } from 'primeng/checkbox';
import { SharedModule } from '../../Core/shared.module';

@NgModule({
  declarations: [
    ResourceListComponent,
    AddEditListComponent,
    ResourceDetailsComponent,
  ],
  imports: [
    CommonModule,
    ResourceRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    ButtonModule,
    ProgressSpinnerModule,
    ToastModule,
    InputTextModule,
    MessageModule,
    RadioButtonModule,
    CardModule,
    DividerModule,
    BadgeModule,
    FileUploadModule,
    DropdownModule,
    CheckboxModule,
    SharedModule,
  ],
})
export class ResourceModule {}
