<p-toast position="top-right"></p-toast>

<div
  class="container py-4"
  style="
    max-width: 1200px;
    min-width: 900px;

    border-radius: 8px;
  "
>
  <!-- Header -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <div class="d-flex align-items-center">
      <a routerLink="/user-management" class="back-button">
        <i class="pi pi-arrow-left"></i>
        <span>User Details</span>
      </a>
    </div>
    <div class="d-flex align-items-center" style="gap: 12px">
      <button
        pButton
        type="button"
        label="Toggle Status"
        icon="pi pi-user-edit"
        class="p-button-outlined p-button-danger"
        (click)="toggleUserStatus()"
      ></button>
      <button
        pButton
        type="button"
        label="Edit"
        icon="pi pi-pencil"
        class="p-button-danger"
        (click)="editUser()"
      ></button>
    </div>
  </div>

  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="text-center py-5">
    <p-progressSpinner
      strokeWidth="4"
      [style]="{ width: '50px', height: '50px' }"
    ></p-progressSpinner>
    <p class="mt-3">Loading user details...</p>
  </div>

  <!-- Error message -->
  <div *ngIf="!isLoading && error" class="alert alert-danger">
    {{ error }}
    <button class="btn btn-link text-danger" (click)="ngOnInit()">
      <i class="bi bi-arrow-clockwise"></i> Retry
    </button>
  </div>

  <!-- Organizer Profile Card -->
  <div
    *ngIf="!isLoading && !error && user"
    class="p-4 mb-4"
    style="background: white; border-radius: 8px"
  >
    <h5 class="font-weight-bold mb-4">Organizer Profile</h5>
    <div class="row">
      <div class="form-group col-md-6 mb-3">
        <label for="name" class="font-weight-bold"
          >Name <span class="text-danger"></span
        ></label>
        <input
          pInputText
          id="name"
          type="text"
          [value]="user.fullName"
          class="form-control"
          readonly
        />
      </div>
      <div class="form-group col-md-6 mb-3">
        <label for="email" class="font-weight-bold"
          >Email <span class="text-danger"></span
        ></label>
        <input
          pInputText
          id="email"
          type="email"
          [value]="user.email"
          class="form-control"
          readonly
        />
      </div>
    </div>
    <div class="row">
      <div class="form-group col-md-4 mb-3">
        <label for="website" class="font-weight-bold">Website</label>
        <input
          pInputText
          id="website"
          type="text"
          [value]="user.website"
          class="form-control"
          readonly
        />
      </div>
      <div class="form-group col-md-4 mb-3">
        <label for="role" class="font-weight-bold"
          >Role <span class="text-danger"></span
        ></label>
        <input
          pInputText
          id="role"
          type="text"
          [value]="user.roles"
          class="form-control"
          readonly
        />
      </div>
      <div class="form-group col-md-4 mb-3 status-group">
        <label class="status-label">Status:</label>
        <input
          class="form-check-input status-radio"
          type="radio"
          id="statusActive"
          name="status"
          [checked]="user.isActive"
          disabled
        />
        <label class="status-active" for="statusActive">Active</label>
        <input
          class="form-check-input status-radio"
          type="radio"
          id="statusInactive"
          name="status"
          [checked]="user.isActive === false"
          disabled
        />
        <label class="status-inactive" for="statusInactive">Inactive</label>
      </div>
    </div>
    <div class="row">
      <div class="form-group col-md-12 mb-0">
        <label for="description" class="font-weight-bold">Description</label>
        <textarea
          pInputTextarea
          id="description"
          rows="3"
          [value]="user.description"
          class="form-control"
          readonly
        ></textarea>
      </div>
    </div>
  </div>

  <!-- Social Media and Marketing Card -->
  <div
    *ngIf="!isLoading && !error && user"
    class="p-4"
    style="background: white; border-radius: 8px"
  >
    <h5 class="font-weight-bold mb-4">Social Media and Marketing</h5>
    <div class="row">
      <div class="form-group col-md-6 mb-3">
        <label class="font-weight-bold">Facebook</label>
        <div class="position-relative">
          <div
            class="position-absolute"
            style="
              top: 50%;
              transform: translateY(-50%);
              left: 10px;
              z-index: 1;
            "
          >
            <i class="pi pi-facebook" style="color: #4267b2"></i>
          </div>
          <input
            pInputText
            type="text"
            [value]="user.facebook || ''"
            class="form-control"
            readonly
            style="padding-left: 35px; width: 100%"
          />
        </div>
      </div>
      <div class="form-group col-md-6 mb-3">
        <label class="font-weight-bold">Twitter (X)</label>
        <div class="position-relative">
          <div
            class="position-absolute"
            style="
              top: 50%;
              transform: translateY(-50%);
              left: 10px;
              z-index: 1;
            "
          >
            <i class="pi pi-twitter" style="color: #1da1f2"></i>
          </div>
          <input
            pInputText
            type="text"
            [value]="user.twitter || ''"
            class="form-control"
            readonly
            style="padding-left: 35px; width: 100%"
          />
        </div>
      </div>
    </div>
  </div>
</div>
