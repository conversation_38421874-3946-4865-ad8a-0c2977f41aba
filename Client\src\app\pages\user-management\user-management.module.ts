import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';

import { UserManagementRoutingModule } from './user-management-routing.module';
import { UserListComponent } from './user-list/user-list.component';
import { AddUserComponent } from './add-user/add-user.component';
import { ButtonModule } from 'primeng/button';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { UserDetailsComponent } from './user-details/user-details.component';
import { InputTextModule } from 'primeng/inputtext';
// Import InputTextarea component directly
import { MessageModule } from 'primeng/message';
import { RadioButtonModule } from 'primeng/radiobutton';
import { ToastModule } from 'primeng/toast';
import { DropdownModule } from 'primeng/dropdown';
import { CardModule } from 'primeng/card';
import { DividerModule } from 'primeng/divider';
import { InputMaskModule } from 'primeng/inputmask';
import { PanelModule } from 'primeng/panel';
import { TableModule } from 'primeng/table';
import { PaginatorModule } from 'primeng/paginator';
import { TagModule } from 'primeng/tag';
import { TooltipModule } from 'primeng/tooltip';
import { RippleModule } from 'primeng/ripple';
import { SharedModule } from '../../Core/shared.module';

@NgModule({
  declarations: [UserListComponent, AddUserComponent, UserDetailsComponent],
  imports: [
    CommonModule,
    FormsModule,
    UserManagementRoutingModule,
    ButtonModule,
    ProgressSpinnerModule,
    ReactiveFormsModule,
    InputTextModule,
    MessageModule,
    RadioButtonModule,
    ToastModule,
    DropdownModule,
    CardModule,
    DividerModule,
    InputMaskModule,
    PanelModule,
    TableModule,
    PaginatorModule,
    TagModule,
    TooltipModule,
    RippleModule,
    SharedModule,
  ],
  providers: [ProgressSpinnerModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class UserManagementModule {}
