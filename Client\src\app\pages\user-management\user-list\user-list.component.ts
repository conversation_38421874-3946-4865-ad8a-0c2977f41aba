import { Component, inject, OnInit } from '@angular/core';
import { UserDetailsService } from '../../../Core/Services/UserDetails.service';
import { Router } from '@angular/router';
import { User } from '../../../Core/Models/User';
import { MessageService } from 'primeng/api';
import { finalize } from 'rxjs';
import { FormBuilder, FormGroup } from '@angular/forms';
import { debounceTime } from 'rxjs/operators';
import { AuthService } from '../../../Core/Services/auth.service';

@Component({
  selector: 'app-user-list',
  standalone: false,
  templateUrl: './user-list.component.html',
  styleUrl: './user-list.component.scss',
  providers: [MessageService],
})
export class UserListComponent implements OnInit {
  Math = Math;
  users: User[] = [];
  error: string | null = null;
  isLoading: boolean = false;
  router = inject(Router);
  showFilters: boolean = false;
  filterForm: FormGroup;
  isGlobalAdmin: boolean = false;

  // Pagination
  currentPage: number = 1;
  pageSize: number = 10;
  totalItems: number = 0;
  totalPages: number = 0;

  // Sorting
  currentSortField?: string;
  currentSortOrder: 'asc' | 'desc' = 'asc';

  // Add these properties to your component class
  roleOptions: { name: string; value: string }[] = [
    { name: 'All Roles', value: '' },
    { name: 'Global Admin', value: 'Global Admin' },
    { name: 'Department Admin', value: 'Department Admin' },
    { name: 'Event Organizer', value: 'Event Organizer' },
  ];

  statusOptions: { name: string; value: string }[] = [
    { name: 'All Status', value: '' },
    { name: 'Active', value: 'active' },
    { name: 'Inactive', value: 'inactive' },
  ];

  constructor(
    private readonly userService: UserDetailsService,
    private readonly messageService: MessageService,
    private fb: FormBuilder,
    private authService: AuthService,
  ) {
    this.filterForm = this.fb.group({
      name: [''],
      createdByName: [''],
      role: [''],
      status: [''],
    });

    // Debounce filter changes
    this.filterForm.valueChanges.pipe(debounceTime(300)).subscribe(() => {
      this.currentPage = 1; // Reset to first page when filters change
      this.loadUsers();
    });
  }

  ngOnInit() {
    this.loadUsers();
    this.checkUserRole();
  }

  checkUserRole(): void {
    const userInfo = this.authService.getUserInfo();
    if (userInfo && userInfo.role) {
      // Check if user has Global Admin role
      this.isGlobalAdmin = Array.isArray(userInfo.role)
        ? userInfo.role.includes('Global Admin')
        : userInfo.role === 'Global Admin';
    }
  }

  loadUsers(): void {
    this.error = null;
    this.isLoading = true;

    const params = {
      pageNumber: this.currentPage,
      pageSize: this.pageSize,
      sortField: this.currentSortField,
      sortOrder: this.currentSortOrder,
      filters: this.filterForm.value,
    };

    this.userService
      .getUsers(params)
      .pipe(finalize(() => (this.isLoading = false)))
      .subscribe({
        next: (response) => {
          this.users = response.items;
          this.totalItems = response.totalItems;
          this.totalPages = response.totalPages;
        },
        error: (err) => {
          this.error = err.message;
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: err.message || 'Failed to load users. Please try again.',
          });
        },
      });
  }

  toggleFilters(): void {
    this.showFilters = !this.showFilters;
  }

  clearFilters(): void {
    this.filterForm.reset();
    this.currentPage = 1;
    this.loadUsers();
  }

  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      this.loadUsers();
    }
  }

  onPaginatorPageChange(event: any): void {
    const newPage = Math.floor(event.first / event.rows) + 1;
    if (newPage !== this.currentPage) {
      this.currentPage = newPage;
      this.pageSize = event.rows;
      this.loadUsers();
    }
  }

  get pages(): number[] {
    if (this.totalPages <= 5) {
      return Array.from({ length: this.totalPages }, (_, i) => i + 1);
    }

    if (this.currentPage <= 3) {
      return [1, 2, 3, 4, 5];
    }

    if (this.currentPage >= this.totalPages - 2) {
      return Array.from({ length: 5 }, (_, i) => this.totalPages - 4 + i);
    }

    return Array.from({ length: 5 }, (_, i) => this.currentPage - 2 + i);
  }

  onSort(event: any): void {
    this.currentSortField = event.field;
    this.currentSortOrder = event.order === 1 ? 'asc' : 'desc';
    this.loadUsers();
  }

  // Navigation methods
  viewUserDetails(userId: string): void {
    this.router.navigate(['/user-management/user-details', userId]);
  }

  addUser(): void {
    this.router.navigate(['/user-management/add-user']);
  }

  editUser(userId: string): void {
    this.router.navigate(['/user-management/edit-user', userId]);
  }

  toggleUserStatus(user: User): void {
    const newStatus = !user.isActive;
    const action = newStatus ? 'activate' : 'deactivate';

    // Check if user is trying to deactivate themselves
    const currentUser = this.authService.getUserInfo();
    if (!newStatus && currentUser && currentUser.id === user.id) {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'You cannot deactivate your own account.',
      });
      return;
    }

    this.userService.toggleUserStatus(user.id, newStatus).subscribe({
      next: (response) => {
        if (response.isSuccess) {
          user.isActive = newStatus;
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: `User ${action}d successfully.`,
          });
        } else {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: response.message || `Failed to ${action} user.`,
          });
        }
      },
      error: (err) => {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: err.message || `Failed to ${action} user.`,
        });
      },
    });
  }
}
