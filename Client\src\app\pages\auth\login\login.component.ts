import { Component, inject, OnInit, Renderer2 } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from '../../../Core/Services/auth.service';
import { LoginRequest } from '../../../Core/Models/login-request';
import { ToastrService } from 'ngx-toastr';
import { MessageService } from 'primeng/api';

@Component({
  selector: 'app-login',
  standalone: false,
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss',
})
export class LoginComponent implements OnInit {
  loginForm!: FormGroup;
  fb = inject(FormBuilder);
  router = inject(Router);
  errorMessage: string | null = null;
  isLoading: boolean = false;

  authService = inject(AuthService);

  constructor(private messageService: MessageService) {}

  ngOnInit(): void {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required]],
    });
  }

  onSubmit(): void {
    if (this.loginForm.invalid) {
      return;
    }

    this.isLoading = true;
    const loginData: LoginRequest = this.loginForm.value;

    this.authService.login(loginData).subscribe({
      next: (res) => {
        if (!res.isSuccess) {
          this.isLoading = false;
          this.messageService.add({
            severity: 'error',
            summary: 'Login Failed',
            detail: res.message || 'Login failed',
            styleClass: 'custom-error-toast',
            life: 4000,
          });
          this.errorMessage = res.message || 'Login failed';
          return;
        }

        if (res.message && res.message.toLowerCase().includes('inactive')) {
          this.isLoading = false;
          this.messageService.add({
            severity: 'warn',
            summary: 'Account Inactive',
            detail:
              'Your account is inactive. Please contact an administrator to activate your account.',
            styleClass: 'custom-warning-toast',
            life: 6000,
          });
          this.errorMessage =
            'Your account is inactive. Please contact an administrator to activate your account.';
          return;
        }

        if (res.data?.RequiresOtp === true || res.data?.requiresOtp === true) {
          this.isLoading = false;
          this.messageService.add({
            severity: 'info',
            summary: 'OTP Required',
            detail: 'Please check your email for the OTP',
            life: 4000,
          });

          const userId = res.data?.UserId || res.data?.userId;
          this.router.navigate(['/auth/VerifyOtp'], {
            state: { userId: userId },
          });
        } else {
          this.messageService.add({
            severity: 'success',
            summary: 'Login Success',
            detail: res.message,
            styleClass: 'custom-success-toast',
            life: 4000,
          });
          this.isLoading = false;
          this.router.navigate(['/dashboard']);
        }
      },
      error: (error) => {
        this.isLoading = false;
        const errorMessage =
          error.error?.message || 'Invalid email or password.';
        this.messageService.add({
          severity: 'error',
          summary: 'Login Failed',
          detail: errorMessage,
          styleClass: 'custom-error-toast',
          life: 4000,
        });
        this.errorMessage = errorMessage;
      },
    });
  }
}
