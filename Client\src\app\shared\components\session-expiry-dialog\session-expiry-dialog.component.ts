import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { interval, Subscription } from 'rxjs';

@Component({
  selector: 'app-session-expiry-dialog',
  standalone: true,
  imports: [CommonModule, ButtonModule],
  template: `
    <div class="session-expiry-dialog">
      <div class="p-3">
        <p class="mb-3">
          Your session will expire in
          <span class="font-bold text-danger">{{ displayTime }}</span
          >.
        </p>
        <p class="mb-4">Would you like to extend your session or log out?</p>

        <div class="d-flex justify-content-end gap-2">
          <button
            pButton
            label="Log Out"
            class="p-button-outlined p-button-danger"
            (click)="logout()"
          ></button>
          <button
            pButton
            label="Extend Session"
            class="p-button-primary"
            (click)="extendSession()"
          ></button>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .session-expiry-dialog {
        min-width: 300px;
      }
      .text-danger {
        color: #dc3545;
      }
    `,
  ],
})
export class SessionExpiryDialogComponent implements OnInit, OnDestroy {
  timeRemaining: number = 0;
  displayTime: string = '';
  private timerSubscription: Subscription | null = null;

  constructor(
    public ref: DynamicDialogRef,
    public config: DynamicDialogConfig,
  ) {}

  ngOnInit(): void {
    // Get initial time remaining from dialog data
    this.timeRemaining = this.config.data?.timeRemaining || 60;
    this.updateDisplayTime();

    // Start countdown timer
    this.timerSubscription = interval(1000).subscribe(() => {
      this.timeRemaining--;
      this.updateDisplayTime();

      // Auto-close and logout when timer reaches 0
      if (this.timeRemaining <= 0) {
        this.logout();
      }
    });
  }

  updateDisplayTime(): void {
    const minutes = Math.floor(this.timeRemaining / 60);
    const seconds = this.timeRemaining % 60;
    this.displayTime = `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  extendSession(): void {
    this.ref.close('extend');
  }

  logout(): void {
    this.ref.close('logout');
  }

  ngOnDestroy(): void {
    if (this.timerSubscription) {
      this.timerSubscription.unsubscribe();
    }
  }
}
